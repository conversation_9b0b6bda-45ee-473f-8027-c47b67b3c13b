/* Phone Input Component Styles */
.phone-input-container {
  width: 100%;
  margin-bottom: 15px;
}

.phone-input-wrapper {
  display: flex;
  width: 100%;
  border-radius: var(--border-radius-medium);
  overflow: hidden;
}

.country-code-select {
  display: flex;
  align-items: center;
  background-color: var(--light-bg-color, #f5f5f5);
  padding: 0 10px;
  border: 1px solid var(--light-gray, #ddd);
  border-right: none;
  border-radius: var(--border-radius-medium) 0 0 var(--border-radius-medium);
}

.phone-icon {
  color: var(--dark-gray, #666);
  font-size: 1.2rem;
  margin-right: 5px;
}

.country-code-dropdown {
  cursor: pointer;
  border: none;
  
}

.country-code-dropdown:focus {
  outline: none;
}

.phone-number-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid var(--light-gray, #ddd);
  border-radius: 0 var(--border-radius-medium) var(--border-radius-medium) 0;
  font-size: var(--basefont, 16px);
  color: var(--text-color, #333);
  background-color: var(--white, #fff);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.phone-number-input:focus {
  border-color: var(--btn-color, #ee3425);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.2);
  outline: none;
}

.phone-number-input::placeholder {
  color: var(--dark-gray, #999);
  opacity: 0.7;
}

.phone-number-input.error {
  border-color: var(--error-color, #dc3545);
}

.phone-error-message {
  color: var(--error-color, #dc3545);
  font-size: 0.85rem;
  margin-top: 5px;
  margin-bottom: 0;
}

/* Disabled state */
.phone-input-wrapper:has(input:disabled),
.phone-input-wrapper:has(select:disabled) {
  opacity: 0.7;
  cursor: not-allowed;
}

.phone-number-input:disabled,
.country-code-dropdown:disabled {
  cursor: not-allowed;
  background-color: var(--disabled-bg, #f9f9f9);
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .country-code-dropdown {
    min-width: 100px;
    padding: 12px 2px;
  }
  
  .phone-number-input {
    padding: 12px 10px;
  }
}
