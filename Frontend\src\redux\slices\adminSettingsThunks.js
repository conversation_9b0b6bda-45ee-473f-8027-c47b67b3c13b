import { createAsyncThunk } from '@reduxjs/toolkit';
import settingsService from '../../services/admin/settingsService';
import {
  setSettingsLoading,
  setSettingsError,
  setAllSettings,
  updateGeneralSettings,
  updateFinancialSettingsNew,
  updateEmailSettings,
  updateSecuritySettings,
  updateSystemSettings,
  updateNotificationSettings,
  addActivity
} from './adminDashboardSlice';

// Fetch all settings
export const fetchAllSettings = createAsyncThunk(
  'admin/fetchAllSettings',
  async (_, { dispatch, rejectWithValue }) => {
    try {
      dispatch(setSettingsLoading(true));
      const response = await settingsService.getAllSettings();
      
      if (response.success) {
        dispatch(setAllSettings(response.data));
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch settings');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch settings';
      dispatch(setSettingsError(errorMessage));
      return rejectWithValue(errorMessage);
    }
  }
);

// Update settings by category
export const updateSettingsByCategory = createAsyncThunk(
  'admin/updateSettingsByCategory',
  async ({ category, settings }, { dispatch, getState, rejectWithValue }) => {
    try {
      dispatch(setSettingsLoading(true));
      const response = await settingsService.updateSettings(category, settings);
      
      if (response.success) {
        // Update the appropriate settings category in state
        switch (category) {
          case 'general':
            dispatch(updateGeneralSettings(settings));
            break;
          case 'financial':
            dispatch(updateFinancialSettingsNew(settings));
            break;
          case 'email':
            dispatch(updateEmailSettings(settings));
            break;
          case 'security':
            dispatch(updateSecuritySettings(settings));
            break;
          case 'system':
            dispatch(updateSystemSettings(settings));
            break;
          case 'notifications':
            dispatch(updateNotificationSettings(settings));
            break;
        }

        // Add activity log
        dispatch(addActivity({
          id: Date.now(),
          type: 'settings_update',
          description: `${category.charAt(0).toUpperCase() + category.slice(1)} settings updated`,
          timestamp: new Date().toISOString(),
          user: getState().auth.user?.firstName || 'Admin',
        }));

        dispatch(setSettingsLoading(false));
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to update settings');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to update settings';
      dispatch(setSettingsError(errorMessage));
      return rejectWithValue(errorMessage);
    }
  }
);

// Update financial settings
export const updateFinancialSettingsThunk = createAsyncThunk(
  'admin/updateFinancialSettings',
  async (settings, { dispatch, getState, rejectWithValue }) => {
    try {
      dispatch(setSettingsLoading(true));
      const response = await settingsService.updateFinancialSettings(settings);
      
      if (response.success) {
        dispatch(updateFinancialSettingsNew(settings));
        
        // Add activity log
        dispatch(addActivity({
          id: Date.now(),
          type: 'financial_settings_update',
          description: 'Financial settings updated',
          timestamp: new Date().toISOString(),
          user: getState().auth.user?.firstName || 'Admin',
        }));

        dispatch(setSettingsLoading(false));
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to update financial settings');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to update financial settings';
      dispatch(setSettingsError(errorMessage));
      return rejectWithValue(errorMessage);
    }
  }
);

// Update email settings
export const updateEmailSettingsThunk = createAsyncThunk(
  'admin/updateEmailSettings',
  async (settings, { dispatch, getState, rejectWithValue }) => {
    try {
      dispatch(setSettingsLoading(true));
      const response = await settingsService.updateEmailSettings(settings);
      
      if (response.success) {
        dispatch(updateEmailSettings(settings));
        
        // Add activity log
        dispatch(addActivity({
          id: Date.now(),
          type: 'email_settings_update',
          description: 'Email settings updated',
          timestamp: new Date().toISOString(),
          user: getState().auth.user?.firstName || 'Admin',
        }));

        dispatch(setSettingsLoading(false));
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to update email settings');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to update email settings';
      dispatch(setSettingsError(errorMessage));
      return rejectWithValue(errorMessage);
    }
  }
);

// Update security settings
export const updateSecuritySettingsThunk = createAsyncThunk(
  'admin/updateSecuritySettings',
  async (settings, { dispatch, getState, rejectWithValue }) => {
    try {
      dispatch(setSettingsLoading(true));
      const response = await settingsService.updateSecuritySettings(settings);
      
      if (response.success) {
        dispatch(updateSecuritySettings(settings));
        
        // Add activity log
        dispatch(addActivity({
          id: Date.now(),
          type: 'security_settings_update',
          description: 'Security settings updated',
          timestamp: new Date().toISOString(),
          user: getState().auth.user?.firstName || 'Admin',
        }));

        dispatch(setSettingsLoading(false));
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to update security settings');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to update security settings';
      dispatch(setSettingsError(errorMessage));
      return rejectWithValue(errorMessage);
    }
  }
);

// Update system settings
export const updateSystemSettingsThunk = createAsyncThunk(
  'admin/updateSystemSettings',
  async (settings, { dispatch, getState, rejectWithValue }) => {
    try {
      dispatch(setSettingsLoading(true));
      const response = await settingsService.updateSystemSettings(settings);
      
      if (response.success) {
        dispatch(updateSystemSettings(settings));
        
        // Add activity log
        dispatch(addActivity({
          id: Date.now(),
          type: 'system_settings_update',
          description: 'System settings updated',
          timestamp: new Date().toISOString(),
          user: getState().auth.user?.firstName || 'Admin',
        }));

        dispatch(setSettingsLoading(false));
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to update system settings');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to update system settings';
      dispatch(setSettingsError(errorMessage));
      return rejectWithValue(errorMessage);
    }
  }
);

// Reset settings to default
export const resetSettingsToDefaultThunk = createAsyncThunk(
  'admin/resetSettingsToDefault',
  async (category, { dispatch, getState, rejectWithValue }) => {
    try {
      dispatch(setSettingsLoading(true));
      const response = await settingsService.resetSettings(category);
      
      if (response.success) {
        // Fetch updated settings after reset
        dispatch(fetchAllSettings());
        
        // Add activity log
        dispatch(addActivity({
          id: Date.now(),
          type: 'settings_reset',
          description: `${category.charAt(0).toUpperCase() + category.slice(1)} settings reset to default`,
          timestamp: new Date().toISOString(),
          user: getState().auth.user?.firstName || 'Admin',
        }));

        return response.data;
      } else {
        throw new Error(response.message || 'Failed to reset settings');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to reset settings';
      dispatch(setSettingsError(errorMessage));
      return rejectWithValue(errorMessage);
    }
  }
);

// Export settings
export const exportSettingsThunk = createAsyncThunk(
  'admin/exportSettings',
  async (_, { dispatch, getState, rejectWithValue }) => {
    try {
      const response = await settingsService.exportSettings();
      
      if (response.success) {
        // Add activity log
        dispatch(addActivity({
          id: Date.now(),
          type: 'settings_export',
          description: 'Settings exported',
          timestamp: new Date().toISOString(),
          user: getState().auth.user?.firstName || 'Admin',
        }));

        return response.data;
      } else {
        throw new Error(response.message || 'Failed to export settings');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to export settings';
      return rejectWithValue(errorMessage);
    }
  }
);

// Import settings
export const importSettingsThunk = createAsyncThunk(
  'admin/importSettings',
  async (settingsData, { dispatch, getState, rejectWithValue }) => {
    try {
      dispatch(setSettingsLoading(true));
      const response = await settingsService.importSettings(settingsData);
      
      if (response.success) {
        // Fetch updated settings after import
        dispatch(fetchAllSettings());
        
        // Add activity log
        dispatch(addActivity({
          id: Date.now(),
          type: 'settings_import',
          description: 'Settings imported',
          timestamp: new Date().toISOString(),
          user: getState().auth.user?.firstName || 'Admin',
        }));

        return response.data;
      } else {
        throw new Error(response.message || 'Failed to import settings');
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to import settings';
      dispatch(setSettingsError(errorMessage));
      return rejectWithValue(errorMessage);
    }
  }
);
