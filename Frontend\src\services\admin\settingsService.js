import api from '../api';

/**
 * Admin Settings Service
 * Handles all admin settings API calls
 */

// Get all settings
export const getAllSettings = async () => {
  try {
    const response = await api.get('/admin/settings');
    return response.data;
  } catch (error) {
    console.error('Error fetching all settings:', error);
    throw error;
  }
};

// Update settings by category
export const updateSettings = async (category, settings) => {
  try {
    const response = await api.put('/admin/settings', {
      category,
      settings
    });
    return response.data;
  } catch (error) {
    console.error(`Error updating ${category} settings:`, error);
    throw error;
  }
};

// Get financial settings
export const getFinancialSettings = async () => {
  try {
    const response = await api.get('/admin/settings/financial');
    return response.data;
  } catch (error) {
    console.error('Error fetching financial settings:', error);
    throw error;
  }
};

// Update financial settings
export const updateFinancialSettings = async (settings) => {
  try {
    const response = await api.put('/admin/settings/financial', settings);
    return response.data;
  } catch (error) {
    console.error('Error updating financial settings:', error);
    throw error;
  }
};

// Get email settings
export const getEmailSettings = async () => {
  try {
    const response = await api.get('/admin/settings/email');
    return response.data;
  } catch (error) {
    console.error('Error fetching email settings:', error);
    throw error;
  }
};

// Update email settings
export const updateEmailSettings = async (settings) => {
  try {
    const response = await api.put('/admin/settings/email', settings);
    return response.data;
  } catch (error) {
    console.error('Error updating email settings:', error);
    throw error;
  }
};

// Get security settings
export const getSecuritySettings = async () => {
  try {
    const response = await api.get('/admin/settings/security');
    return response.data;
  } catch (error) {
    console.error('Error fetching security settings:', error);
    throw error;
  }
};

// Update security settings
export const updateSecuritySettings = async (settings) => {
  try {
    const response = await api.put('/admin/settings/security', settings);
    return response.data;
  } catch (error) {
    console.error('Error updating security settings:', error);
    throw error;
  }
};

// Get system settings
export const getSystemSettings = async () => {
  try {
    const response = await api.get('/admin/settings/system');
    return response.data;
  } catch (error) {
    console.error('Error fetching system settings:', error);
    throw error;
  }
};

// Update system settings
export const updateSystemSettings = async (settings) => {
  try {
    const response = await api.put('/admin/settings/system', settings);
    return response.data;
  } catch (error) {
    console.error('Error updating system settings:', error);
    throw error;
  }
};

// Reset settings to default
export const resetSettings = async (category) => {
  try {
    const response = await api.post('/admin/settings/reset', { category });
    return response.data;
  } catch (error) {
    console.error(`Error resetting ${category} settings:`, error);
    throw error;
  }
};

// Export settings
export const exportSettings = async () => {
  try {
    const response = await api.get('/admin/settings/export');
    return response.data;
  } catch (error) {
    console.error('Error exporting settings:', error);
    throw error;
  }
};

// Import settings
export const importSettings = async (settingsData) => {
  try {
    const response = await api.post('/admin/settings/import', settingsData);
    return response.data;
  } catch (error) {
    console.error('Error importing settings:', error);
    throw error;
  }
};

// Get settings history
export const getSettingsHistory = async () => {
  try {
    const response = await api.get('/admin/settings/history');
    return response.data;
  } catch (error) {
    console.error('Error fetching settings history:', error);
    throw error;
  }
};

const settingsService = {
  getAllSettings,
  updateSettings,
  getFinancialSettings,
  updateFinancialSettings,
  getEmailSettings,
  updateEmailSettings,
  getSecuritySettings,
  updateSecuritySettings,
  getSystemSettings,
  updateSystemSettings,
  resetSettings,
  exportSettings,
  importSettings,
  getSettingsHistory
};

export default settingsService;
