import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { toast } from "react-toastify";
import {
  selectProfile,
  selectLoading,
  selectErrors
} from "../../redux/slices/adminDashboardSlice";
import {
  fetchAllSettings,
  updateSettingsByCategory,
  updateFinancialSettingsThunk,
  updateEmailSettingsThunk,
  updateSecuritySettingsThunk,
  updateSystemSettingsThunk,
  resetSettingsToDefaultThunk
} from "../../redux/slices/adminSettingsThunks";
import AdminLayout from "../../components/admin/AdminLayout";
import FinancialSettings from "../../components/admin/FinancialSettings";
import "../../styles/AdminSettings.css";

// Icons
import { FaCog, FaUser, FaBell, FaLock, FaUpload, FaSave, FaRed<PERSON>, FaSpinner } from "react-icons/fa";
import { MdSecurity, MdNotifications, MdSettings } from "react-icons/md";

const AdminSettings = () => {
  const dispatch = useDispatch();
  const profile = useSelector(selectProfile);
  const settings = useSelector(state => state.adminDashboard.settings);
  const loading = useSelector(selectLoading);
  const errors = useSelector(selectErrors);

  const [activeTab, setActiveTab] = useState("general");
  const [localSettings, setLocalSettings] = useState({});
  const [hasChanges, setHasChanges] = useState(false);

  // Load settings on component mount
  useEffect(() => {
    dispatch(fetchAllSettings());
  }, [dispatch]);

  // Update local settings when Redux settings change
  useEffect(() => {
    if (settings && settings[activeTab]) {
      setLocalSettings(settings[activeTab]);
      setHasChanges(false);
    }
  }, [settings, activeTab]);

  const handleInputChange = (field, value) => {
    setLocalSettings(prev => ({
      ...prev,
      [field]: value
    }));
    setHasChanges(true);
  };

  const handleSave = async () => {
    try {
      let result;

      switch (activeTab) {
        case 'financial':
          result = await dispatch(updateFinancialSettingsThunk(localSettings));
          break;
        case 'email':
          result = await dispatch(updateEmailSettingsThunk(localSettings));
          break;
        case 'security':
          result = await dispatch(updateSecuritySettingsThunk(localSettings));
          break;
        case 'system':
          result = await dispatch(updateSystemSettingsThunk(localSettings));
          break;
        default:
          result = await dispatch(updateSettingsByCategory({
            category: activeTab,
            settings: localSettings
          }));
      }

      if (result.type.endsWith('/fulfilled')) {
        toast.success(`${activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} settings saved successfully!`);
        setHasChanges(false);
      } else {
        throw new Error(result.payload || 'Failed to save settings');
      }
    } catch (error) {
      toast.error(error.message || 'Failed to save settings');
    }
  };

  const handleReset = async () => {
    if (window.confirm(`Are you sure you want to reset ${activeTab} settings to default values?`)) {
      try {
        const result = await dispatch(resetSettingsToDefaultThunk(activeTab));
        if (result.type.endsWith('/fulfilled')) {
          toast.success(`${activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} settings reset to default!`);
          setHasChanges(false);
        } else {
          throw new Error(result.payload || 'Failed to reset settings');
        }
      } catch (error) {
        toast.error(error.message || 'Failed to reset settings');
      }
    }
  };

  const handleLogoUpload = () => {
    // TODO: Implement actual logo upload functionality
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = (e) => {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          handleInputChange('logoUrl', e.target.result);
        };
        reader.readAsDataURL(file);
        toast.success('Logo uploaded successfully!');
      }
    };
    input.click();
  };

  const handleRolePermissionEdit = (role) => {
    toast.info(`Role permission editing for ${role} - Feature coming soon!`);
  };

  const tabs = [
    { id: "general", label: "General Settings", icon: <MdSettings /> },
    { id: "financial", label: "Financial", icon: <FaCog /> },
    { id: "email", label: "Email Settings", icon: <FaBell /> },
    { id: "security", label: "Security", icon: <MdSecurity /> },
    { id: "system", label: "System", icon: <FaCog /> },
    { id: "notifications", label: "Notifications", icon: <MdNotifications /> },
  ];

  return (
    <AdminLayout>
      <div className="AdminSettings">
        {/* Settings Navigation */}
        <div className="AdminSettings__nav">
          {tabs.map(tab => (
            <button
              key={tab.id}
              className={`nav-tab ${activeTab === tab.id ? 'active' : ''}`}
              onClick={() => setActiveTab(tab.id)}
            >
              {tab.icon}
              {tab.label}
            </button>
          ))}
        </div>

        {/* Action Buttons */}
        <div className="AdminSettings__actions">
          <button
            className="btn btn-secondary"
            onClick={handleReset}
            disabled={loading.settings}
          >
            <FaRedo />
            Reset to Default
          </button>
          <button
            className={`btn btn-primary ${hasChanges ? 'has-changes' : ''}`}
            onClick={handleSave}
            disabled={loading.settings || !hasChanges}
          >
            {loading.settings ? <FaSpinner className="spinning" /> : <FaSave />}
            Save Changes
          </button>
        </div>

        {/* Settings Content */}
        <div className="AdminSettings__content">
          {loading.settings && (
            <div className="loading-overlay">
              <FaSpinner className="spinning" />
              <span>Loading settings...</span>
            </div>
          )}

          {errors.settings && (
            <div className="error-message">
              <p>Error loading settings: {errors.settings}</p>
            </div>
          )}

          {activeTab === "general" && (
            <div className="settings-section">
              <div className="section-header">
                <h3>General Settings</h3>
                <p>Configure basic site settings and information</p>
              </div>

              <div className="settings-form">
                <div className="form-group">
                  <label>Site Name</label>
                  <input
                    type="text"
                    value={localSettings.siteName || ''}
                    onChange={(e) => handleInputChange("siteName", e.target.value)}
                    className="form-input"
                    placeholder="Enter site name"
                  />
                </div>

                <div className="form-group">
                  <label>Site Description</label>
                  <textarea
                    value={localSettings.siteDescription || ''}
                    onChange={(e) => handleInputChange("siteDescription", e.target.value)}
                    className="form-textarea"
                    rows="3"
                    placeholder="Enter site description"
                  />
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label>Contact Email</label>
                    <input
                      type="email"
                      value={localSettings.contactEmail || ''}
                      onChange={(e) => handleInputChange("contactEmail", e.target.value)}
                      className="form-input"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div className="form-group">
                    <label>Support Email</label>
                    <input
                      type="email"
                      value={localSettings.supportEmail || ''}
                      onChange={(e) => handleInputChange("supportEmail", e.target.value)}
                      className="form-input"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label>Timezone</label>
                    <select
                      value={localSettings.timezone || 'UTC'}
                      onChange={(e) => handleInputChange("timezone", e.target.value)}
                      className="form-select"
                    >
                      <option value="UTC">UTC</option>
                      <option value="America/New_York">Eastern Time</option>
                      <option value="America/Chicago">Central Time</option>
                      <option value="America/Denver">Mountain Time</option>
                      <option value="America/Los_Angeles">Pacific Time</option>
                    </select>
                  </div>

                  <div className="form-group">
                    <label>Default Language</label>
                    <select
                      value={localSettings.language || 'en'}
                      onChange={(e) => handleInputChange("language", e.target.value)}
                      className="form-select"
                    >
                      <option value="en">English</option>
                      <option value="es">Spanish</option>
                      <option value="fr">French</option>
                    </select>
                  </div>
                </div>

                <div className="form-group">
                  <label>Default Currency</label>
                  <select
                    value={localSettings.currency || 'USD'}
                    onChange={(e) => handleInputChange("currency", e.target.value)}
                    className="form-select"
                  >
                    <option value="USD">USD - US Dollar</option>
                    <option value="EUR">EUR - Euro</option>
                    <option value="GBP">GBP - British Pound</option>
                  </select>
                </div>

                <div className="form-group">
                  <label>Site Logo</label>
                  <div className="logo-upload">
                    <div className="logo-preview">
                      {localSettings.logoUrl ? (
                        <img src={localSettings.logoUrl} alt="Site Logo" />
                      ) : (
                        <div className="logo-placeholder">
                          <FaUpload />
                          <span>No logo uploaded</span>
                        </div>
                      )}
                    </div>
                    <button
                      className="btn btn-outline"
                      onClick={handleLogoUpload}
                    >
                      <FaUpload />
                      Upload Logo
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === "financial" && (
            <FinancialSettings />
          )}

          {activeTab === "email" && (
            <div className="settings-section">
              <div className="section-header">
                <h3>Email Settings</h3>
                <p>Configure SMTP settings and email preferences</p>
              </div>

              <div className="settings-form">
                <div className="form-row">
                  <div className="form-group">
                    <label>SMTP Host</label>
                    <input
                      type="text"
                      value={localSettings.smtpHost || ''}
                      onChange={(e) => handleInputChange("smtpHost", e.target.value)}
                      className="form-input"
                      placeholder="smtp.gmail.com"
                    />
                  </div>

                  <div className="form-group">
                    <label>SMTP Port</label>
                    <input
                      type="number"
                      value={localSettings.smtpPort || ''}
                      onChange={(e) => handleInputChange("smtpPort", parseInt(e.target.value))}
                      className="form-input"
                      placeholder="587"
                    />
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label>SMTP Username</label>
                    <input
                      type="text"
                      value={localSettings.smtpUser || ''}
                      onChange={(e) => handleInputChange("smtpUser", e.target.value)}
                      className="form-input"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div className="form-group">
                    <label>SMTP Password</label>
                    <input
                      type="password"
                      value={localSettings.smtpPassword || ''}
                      onChange={(e) => handleInputChange("smtpPassword", e.target.value)}
                      className="form-input"
                      placeholder="Enter SMTP password"
                    />
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label>From Email</label>
                    <input
                      type="email"
                      value={localSettings.fromEmail || ''}
                      onChange={(e) => handleInputChange("fromEmail", e.target.value)}
                      className="form-input"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div className="form-group">
                    <label>From Name</label>
                    <input
                      type="text"
                      value={localSettings.fromName || ''}
                      onChange={(e) => handleInputChange("fromName", e.target.value)}
                      className="form-input"
                      placeholder="XOSportsHub"
                    />
                  </div>
                </div>

                <div className="toggle-setting">
                  <div className="toggle-info">
                    <span className="toggle-label">Enable Email Notifications</span>
                    <span className="toggle-description">Allow the system to send email notifications</span>
                  </div>
                  <label className="toggle-switch">
                    <input
                      type="checkbox"
                      checked={localSettings.enableNotifications || false}
                      onChange={(e) => handleInputChange("enableNotifications", e.target.checked)}
                    />
                    <span className="slider"></span>
                  </label>
                </div>
              </div>
            </div>
          )}

          {activeTab === "notifications" && (
            <div className="settings-section">
              <div className="section-header">
                <h3>Notification Settings</h3>
                <p>Configure email and push notification preferences</p>
              </div>

              <div className="settings-form">
                <div className="notification-group">
                  <h4>Email Notifications</h4>

                  <div className="toggle-setting">
                    <div className="toggle-info">
                      <span className="toggle-label">Email Notifications</span>
                      <span className="toggle-description">Receive general email notifications</span>
                    </div>
                    <label className="toggle-switch">
                      <input
                        type="checkbox"
                        checked={localSettings.emailNotifications || false}
                        onChange={(e) => handleInputChange("emailNotifications", e.target.checked)}
                      />
                      <span className="slider"></span>
                    </label>
                  </div>

                  <div className="toggle-setting">
                    <div className="toggle-info">
                      <span className="toggle-label">Marketing Emails</span>
                      <span className="toggle-description">Receive marketing and promotional emails</span>
                    </div>
                    <label className="toggle-switch">
                      <input
                        type="checkbox"
                        checked={localSettings.marketingEmails || false}
                        onChange={(e) => handleInputChange("marketingEmails", e.target.checked)}
                      />
                      <span className="slider"></span>
                    </label>
                  </div>

                  <div className="toggle-setting">
                    <div className="toggle-info">
                      <span className="toggle-label">Security Alerts</span>
                      <span className="toggle-description">Receive security-related notifications</span>
                    </div>
                    <label className="toggle-switch">
                      <input
                        type="checkbox"
                        checked={localSettings.securityAlerts || false}
                        onChange={(e) => handleInputChange("securityAlerts", e.target.checked)}
                      />
                      <span className="slider"></span>
                    </label>
                  </div>
                </div>

                <div className="notification-group">
                  <h4>Push Notifications</h4>

                  <div className="toggle-setting">
                    <div className="toggle-info">
                      <span className="toggle-label">Browser Notifications</span>
                      <span className="toggle-description">Receive push notifications in your browser</span>
                    </div>
                    <label className="toggle-switch">
                      <input
                        type="checkbox"
                        checked={localSettings.pushNotifications || false}
                        onChange={(e) => handleInputChange("pushNotifications", e.target.checked)}
                      />
                      <span className="slider"></span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === "security" && (
            <div className="settings-section">
              <div className="section-header">
                <h3>Security Settings</h3>
                <p>Configure security and authentication settings</p>
              </div>

              <div className="settings-form">
                <div className="security-group">
                  <h4>Authentication</h4>

                  <div className="toggle-setting">
                    <div className="toggle-info">
                      <span className="toggle-label">Two-Factor Authentication</span>
                      <span className="toggle-description">Require 2FA for admin accounts</span>
                    </div>
                    <label className="toggle-switch">
                      <input
                        type="checkbox"
                        checked={localSettings.twoFactorAuth || false}
                        onChange={(e) => handleInputChange("twoFactorAuth", e.target.checked)}
                      />
                      <span className="slider"></span>
                    </label>
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label>Session Timeout (seconds)</label>
                      <input
                        type="number"
                        value={localSettings.sessionTimeout || 3600}
                        onChange={(e) => handleInputChange("sessionTimeout", parseInt(e.target.value))}
                        className="form-input"
                        min="300"
                        max="28800"
                        placeholder="3600"
                      />
                    </div>

                    <div className="form-group">
                      <label>Password Expiry (days)</label>
                      <input
                        type="number"
                        value={localSettings.passwordExpiry || 90}
                        onChange={(e) => handleInputChange("passwordExpiry", parseInt(e.target.value))}
                        className="form-input"
                        min="30"
                        max="365"
                        placeholder="90"
                      />
                    </div>
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label>Max Login Attempts</label>
                      <input
                        type="number"
                        value={localSettings.maxLoginAttempts || 5}
                        onChange={(e) => handleInputChange("maxLoginAttempts", parseInt(e.target.value))}
                        className="form-input"
                        min="3"
                        max="10"
                        placeholder="5"
                      />
                    </div>

                    <div className="form-group">
                      <label>Lockout Duration (seconds)</label>
                      <input
                        type="number"
                        value={localSettings.lockoutDuration || 900}
                        onChange={(e) => handleInputChange("lockoutDuration", parseInt(e.target.value))}
                        className="form-input"
                        min="300"
                        max="3600"
                        placeholder="900"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === "system" && (
            <div className="settings-section">
              <div className="section-header">
                <h3>System Settings</h3>
                <p>Configure system-wide settings and preferences</p>
              </div>

              <div className="settings-form">
                <div className="form-row">
                  <div className="form-group">
                    <label>Max File Size (MB)</label>
                    <input
                      type="number"
                      value={localSettings.maxFileSize || 1024}
                      onChange={(e) => handleInputChange("maxFileSize", parseInt(e.target.value))}
                      className="form-input"
                      min="1"
                      max="5120"
                      placeholder="1024"
                    />
                  </div>

                  <div className="form-group">
                    <label>Backup Frequency</label>
                    <select
                      value={localSettings.backupFrequency || 'daily'}
                      onChange={(e) => handleInputChange("backupFrequency", e.target.value)}
                      className="form-select"
                    >
                      <option value="hourly">Hourly</option>
                      <option value="daily">Daily</option>
                      <option value="weekly">Weekly</option>
                      <option value="monthly">Monthly</option>
                    </select>
                  </div>
                </div>

                <div className="form-group">
                  <label>Allowed File Formats</label>
                  <input
                    type="text"
                    value={Array.isArray(localSettings.allowedFormats) ? localSettings.allowedFormats.join(', ') : ''}
                    onChange={(e) => handleInputChange("allowedFormats", e.target.value.split(', ').map(f => f.trim()))}
                    className="form-input"
                    placeholder="jpg, jpeg, png, gif, pdf, mp4, mov, avi"
                  />
                  <small className="form-help">Separate formats with commas</small>
                </div>

                <div className="toggle-setting">
                  <div className="toggle-info">
                    <span className="toggle-label">Maintenance Mode</span>
                    <span className="toggle-description">Enable maintenance mode to restrict access</span>
                  </div>
                  <label className="toggle-switch">
                    <input
                      type="checkbox"
                      checked={localSettings.maintenanceMode || false}
                      onChange={(e) => handleInputChange("maintenanceMode", e.target.checked)}
                    />
                    <span className="slider"></span>
                  </label>
                </div>

                <div className="toggle-setting">
                  <div className="toggle-info">
                    <span className="toggle-label">Debug Mode</span>
                    <span className="toggle-description">Enable debug mode for development</span>
                  </div>
                  <label className="toggle-switch">
                    <input
                      type="checkbox"
                      checked={localSettings.debugMode || false}
                      onChange={(e) => handleInputChange("debugMode", e.target.checked)}
                    />
                    <span className="slider"></span>
                  </label>
                </div>

                <div className="toggle-setting">
                  <div className="toggle-info">
                    <span className="toggle-label">Cache Enabled</span>
                    <span className="toggle-description">Enable caching for better performance</span>
                  </div>
                  <label className="toggle-switch">
                    <input
                      type="checkbox"
                      checked={localSettings.cacheEnabled !== false}
                      onChange={(e) => handleInputChange("cacheEnabled", e.target.checked)}
                    />
                    <span className="slider"></span>
                  </label>
                </div>
              </div>
            </div>
          )}

          {activeTab === "roles" && (
            <div className="settings-section">
              <div className="section-header">
                <h3>User Roles & Permissions</h3>
                <p>Manage user roles and their permissions</p>
              </div>

              <div className="roles-grid">
                <div className="role-card">
                  <div className="role-header">
                    <h4>Admin</h4>
                    <span className="role-count">2 users</span>
                  </div>
                  <div className="role-permissions">
                    <span className="permission-tag">Full Access</span>
                    <span className="permission-tag">User Management</span>
                    <span className="permission-tag">Content Management</span>
                    <span className="permission-tag">System Settings</span>
                  </div>
                  <button
                    className="btn btn-outline"
                    onClick={() => handleRolePermissionEdit('Admin')}
                  >
                    Edit Permissions
                  </button>
                </div>

                <div className="role-card">
                  <div className="role-header">
                    <h4>Seller</h4>
                    <span className="role-count">89 users</span>
                  </div>
                  <div className="role-permissions">
                    <span className="permission-tag">Content Upload</span>
                    <span className="permission-tag">Sales Management</span>
                    <span className="permission-tag">Profile Management</span>
                  </div>
                  <button
                    className="btn btn-outline"
                    onClick={() => handleRolePermissionEdit('Seller')}
                  >
                    Edit Permissions
                  </button>
                </div>

                <div className="role-card">
                  <div className="role-header">
                    <h4>Buyer</h4>
                    <span className="role-count">1,247 users</span>
                  </div>
                  <div className="role-permissions">
                    <span className="permission-tag">Content Purchase</span>
                    <span className="permission-tag">Profile Management</span>
                    <span className="permission-tag">Order History</span>
                  </div>
                  <button
                    className="btn btn-outline"
                    onClick={() => handleRolePermissionEdit('Buyer')}
                  >
                    Edit Permissions
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Save Button */}
          <div className="settings-actions">
            <button className="btn btn-primary" onClick={handleSave}>
              <FaSave />
              Save Changes
            </button>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminSettings;
